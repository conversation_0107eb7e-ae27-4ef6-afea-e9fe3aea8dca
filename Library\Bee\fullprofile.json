{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 7132, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 7132, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 7132, "tid": 47, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 7132, "tid": 47, "ts": 1750349492458463, "dur": 1257, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492481712, "dur": 882, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 7132, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 7132, "tid": 1, "ts": 1750349491885729, "dur": 26659, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750349491912393, "dur": 88882, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 7132, "tid": 1, "ts": 1750349492001294, "dur": 57066, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492482599, "dur": 291, "ph": "X", "name": "", "args": {}}, {"pid": 7132, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491870366, "dur": 673, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491871042, "dur": 547220, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491872063, "dur": 11766, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491883840, "dur": 5852, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491889701, "dur": 360, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890067, "dur": 14, "ph": "X", "name": "ProcessMessages 20573", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890082, "dur": 260, "ph": "X", "name": "ReadAsync 20573", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890345, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890348, "dur": 77, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890427, "dur": 1, "ph": "X", "name": "ProcessMessages 1555", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491890430, "dur": 1421, "ph": "X", "name": "ReadAsync 1555", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491891855, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491891857, "dur": 197, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892058, "dur": 4, "ph": "X", "name": "ProcessMessages 5042", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892063, "dur": 791, "ph": "X", "name": "ReadAsync 5042", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892856, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892858, "dur": 107, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892968, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491892970, "dur": 228, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893204, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893253, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893254, "dur": 207, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893465, "dur": 1, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893467, "dur": 209, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893683, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893739, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893743, "dur": 158, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893904, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893907, "dur": 40, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893952, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491893954, "dur": 175, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894134, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894172, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894175, "dur": 49, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894227, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894229, "dur": 39, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894271, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894273, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894295, "dur": 369, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894670, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894728, "dur": 1, "ph": "X", "name": "ProcessMessages 1701", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894730, "dur": 63, "ph": "X", "name": "ReadAsync 1701", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894807, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894836, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491894839, "dur": 242, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895087, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895133, "dur": 1, "ph": "X", "name": "ProcessMessages 1290", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895135, "dur": 33, "ph": "X", "name": "ReadAsync 1290", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895171, "dur": 11, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895184, "dur": 27, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895213, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895215, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895244, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895246, "dur": 34, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895282, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895284, "dur": 40, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895326, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895328, "dur": 112, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895442, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895445, "dur": 50, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895497, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895499, "dur": 52, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895556, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895585, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895587, "dur": 374, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895965, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491895967, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896023, "dur": 2, "ph": "X", "name": "ProcessMessages 2056", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896026, "dur": 110, "ph": "X", "name": "ReadAsync 2056", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896141, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896143, "dur": 71, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896218, "dur": 3, "ph": "X", "name": "ProcessMessages 2634", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896222, "dur": 64, "ph": "X", "name": "ReadAsync 2634", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896288, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896290, "dur": 155, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896450, "dur": 41, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896494, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896495, "dur": 159, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896660, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896764, "dur": 3, "ph": "X", "name": "ProcessMessages 2642", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896768, "dur": 106, "ph": "X", "name": "ReadAsync 2642", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896878, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896880, "dur": 63, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896946, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896948, "dur": 28, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491896978, "dur": 132, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897114, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897159, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897161, "dur": 123, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897302, "dur": 46, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897350, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897352, "dur": 33, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897389, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897392, "dur": 39, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897433, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897435, "dur": 67, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897504, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897507, "dur": 50, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897559, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897561, "dur": 34, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897597, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897599, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897631, "dur": 50, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897685, "dur": 49, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897737, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897738, "dur": 39, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897780, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897782, "dur": 34, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897820, "dur": 36, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897860, "dur": 112, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897976, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491897979, "dur": 59, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898040, "dur": 2, "ph": "X", "name": "ProcessMessages 1709", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898043, "dur": 41, "ph": "X", "name": "ReadAsync 1709", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898087, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898089, "dur": 195, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898291, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898346, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898349, "dur": 91, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898442, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898445, "dur": 127, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898574, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898577, "dur": 49, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898628, "dur": 1, "ph": "X", "name": "ProcessMessages 1251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898631, "dur": 69, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898705, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898738, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898742, "dur": 45, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898799, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898801, "dur": 121, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898925, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491898927, "dur": 293, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899225, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899318, "dur": 2, "ph": "X", "name": "ProcessMessages 2004", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899321, "dur": 196, "ph": "X", "name": "ReadAsync 2004", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899522, "dur": 47, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899571, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899573, "dur": 33, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899610, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899614, "dur": 174, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899793, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899795, "dur": 50, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899849, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899851, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491899884, "dur": 3399, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491903290, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491903293, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491903346, "dur": 1892, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905243, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905364, "dur": 11, "ph": "X", "name": "ProcessMessages 1872", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905375, "dur": 43, "ph": "X", "name": "ReadAsync 1872", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905422, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905425, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905490, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905521, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905565, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905588, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905616, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905641, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905663, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905687, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905733, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905764, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905766, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905794, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905796, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905830, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905854, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905885, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905906, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905929, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491905953, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906027, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906060, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906082, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906105, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906130, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906151, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906173, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906194, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906216, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906240, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906273, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906297, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906326, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906350, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906388, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906419, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906443, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906487, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906516, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906540, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906619, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906645, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906693, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906722, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906745, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906786, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906810, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906832, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906946, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906974, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491906997, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907020, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907071, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907095, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907118, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907142, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907164, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907187, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907226, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907251, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907274, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907296, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907328, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907353, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907385, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907410, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907412, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907480, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907506, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907507, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907599, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907643, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907644, "dur": 128, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907776, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907777, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907802, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907851, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907872, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907900, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907919, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907940, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491907973, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908001, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908033, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908056, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908088, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908109, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908187, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908211, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908214, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908275, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908297, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908331, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908352, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908372, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908394, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908415, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908436, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908509, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908537, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908638, "dur": 173, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491908815, "dur": 3365, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912189, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912228, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912231, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912259, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912286, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912620, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912647, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912653, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912689, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912691, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491912734, "dur": 390, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913128, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913170, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913194, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913217, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913248, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913274, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491913276, "dur": 1620, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491914902, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491914905, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491914946, "dur": 797, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491915748, "dur": 68, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491915820, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491915822, "dur": 413, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916242, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916311, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916313, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916348, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916350, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916393, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916418, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916420, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916451, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916478, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916558, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491916596, "dur": 945, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917546, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917549, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917590, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917593, "dur": 62, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917661, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917685, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917722, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917757, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491917759, "dur": 379, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918244, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918246, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918293, "dur": 254, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918550, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918596, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491918598, "dur": 574, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919265, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919303, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919305, "dur": 308, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919619, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919646, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919668, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919728, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919749, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919773, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919870, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349491919906, "dur": 469657, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492389574, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492389579, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492389627, "dur": 3441, "ph": "X", "name": "ProcessMessages 2096", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492393077, "dur": 9377, "ph": "X", "name": "ReadAsync 2096", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492402464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492402467, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492402534, "dur": 536, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 7132, "tid": 12884901888, "ts": 1750349492403075, "dur": 9436, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492482892, "dur": 484, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 7132, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 7132, "tid": 8589934592, "ts": 1750349491862638, "dur": 195834, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 7132, "tid": 8589934592, "ts": 1750349492058476, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 7132, "tid": 8589934592, "ts": 1750349492058483, "dur": 3843, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492483378, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 7132, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 7132, "tid": 4294967296, "ts": 1750349491400683, "dur": 1020502, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 7132, "tid": 4294967296, "ts": 1750349491536722, "dur": 59446, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 7132, "tid": 4294967296, "ts": 1750349492446696, "dur": 6746, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 7132, "tid": 4294967296, "ts": 1750349492451980, "dur": 64, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 7132, "tid": 4294967296, "ts": 1750349492453524, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492483384, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750349491612459, "dur": 162, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349491612800, "dur": 6320, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349491619475, "dur": 7687, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349491627518, "dur": 303, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750349491627821, "dur": 928, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349491629723, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C8FB050F2C14EF55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491630050, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491630120, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491630196, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491630263, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6648D96BDBBB29C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491630529, "dur": 249346, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491881208, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491882289, "dur": 8879, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_997BF679851B602C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491892981, "dur": 580, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750349491894136, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750349491895663, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491896443, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750349491897164, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750349491897653, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750349491897784, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750349491898108, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750349491899572, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750349491900222, "dur": 200, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491900614, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750349491628775, "dur": 272047, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349491900832, "dur": 494271, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349492395105, "dur": 305, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349492402761, "dur": 54, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349492402841, "dur": 2595, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750349491629332, "dur": 272457, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491901796, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750349491904158, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491904244, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750349491905542, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491906268, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A3EA7E1937C99F5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750349491906366, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750349491906536, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491906614, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750349491906974, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491907100, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491907380, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491907491, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750349491907795, "dur": 5260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750349491913056, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491913239, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491913745, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491914229, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491914706, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491915783, "dur": 856, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750349491915296, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491917108, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491917273, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491917333, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491918095, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491918415, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491918601, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491919126, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491919245, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750349491919417, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491919958, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750349491920755, "dur": 474377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491629196, "dur": 272020, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491901726, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491904607, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491905526, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491905695, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491906245, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750349491906817, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491906878, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491907156, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750349491907463, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750349491908043, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750349491908175, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491908393, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750349491908614, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750349491909023, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491909164, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750349491909481, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491909970, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491910394, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491910810, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491911665, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491912287, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491912925, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491913597, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750349491914019, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491914145, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491914246, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491914950, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491915777, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Runtime\\SplineType.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750349491915431, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491916817, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491917227, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491917345, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491918090, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491918422, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491918597, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491919123, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491919379, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491919975, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491920601, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349491920701, "dur": 150718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750349492071420, "dur": 323686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491629135, "dur": 271713, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491901628, "dur": 2584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491904214, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491904576, "dur": 1668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491906271, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491906419, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491906551, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491907146, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491907386, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491907474, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491907668, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491907908, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491907994, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491908046, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491908304, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491908474, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750349491909084, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491909309, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491909410, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491909893, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491910304, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491910743, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491911356, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491911826, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491912261, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491912771, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491913120, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491914352, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750349491914325, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491915835, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491916638, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491916828, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491917374, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491918100, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491918400, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491918456, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491918635, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750349491919087, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491919401, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491920310, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750349491920632, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750349491920832, "dur": 474295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491630089, "dur": 271738, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491901828, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491904219, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491904458, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491905518, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491906128, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750349491906548, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750349491906770, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491906921, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491907043, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491907103, "dur": 9193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750349491916298, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491916455, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491916651, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750349491917196, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491917371, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750349491917524, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750349491917952, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491918098, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491918406, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491918605, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491919258, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750349491919387, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491919970, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750349491920722, "dur": 474376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491629302, "dur": 272469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491901782, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750349491904291, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491904627, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491905296, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_6321B8555A1A761D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750349491906170, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750349491906533, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491906687, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491906885, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491907227, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491907374, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491907766, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491908146, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750349491908457, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750349491908614, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491908769, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Abilities.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491909253, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491909403, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750349491909616, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491910202, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491910823, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491911827, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491912253, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491912753, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491913469, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491914227, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491914948, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491915597, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491916209, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491916992, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491917221, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491917347, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491918085, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491918446, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750349491918607, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491918699, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750349491919031, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491919147, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491919255, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491919408, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491919926, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491919988, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491920612, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349491920699, "dur": 142588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349492070676, "dur": 645, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1750349492063289, "dur": 8040, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750349492071330, "dur": 323792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491629369, "dur": 272431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491901806, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750349491904253, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491904561, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750349491904791, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750349491905158, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A02BE6282932C1CB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750349491905516, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491906116, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491906230, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750349491906683, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491906771, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491906865, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750349491907413, "dur": 6079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750349491913493, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491913587, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Status.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750349491914046, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491914154, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750349491914511, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750349491916037, "dur": 474830, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750349491629520, "dur": 272290, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491901815, "dur": 2510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491904456, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491904964, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491905040, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491905431, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_02EB6148B760FC69.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491906228, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491906725, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491907003, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491907082, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491907269, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491907405, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491907598, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491907757, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491908018, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750349491908117, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491908320, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491908462, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491908749, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491908827, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491909001, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491909172, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Actions.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491909331, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750349491909611, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\UsageUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750349491909611, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491910821, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491911627, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491912255, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491912935, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491913687, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491914393, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491915126, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491915823, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491916514, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491916816, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491917241, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491917376, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491918079, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491918487, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750349491918623, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491918710, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750349491919131, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491919310, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491919394, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491919969, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750349491920729, "dur": 474414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491629711, "dur": 272107, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491901823, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491904298, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491904416, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491904500, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491904618, "dur": 1572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491906194, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750349491906559, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491906693, "dur": 10396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491917091, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491917267, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491917428, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491918267, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491918367, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491918446, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491918632, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491919072, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491919279, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491919405, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491919841, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491919922, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750349491920068, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491920209, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750349491920623, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750349491920754, "dur": 474379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750349492411063, "dur": 1744, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 7132, "tid": 47, "ts": 1750349492485737, "dur": 2396, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 7132, "tid": 47, "ts": 1750349492488256, "dur": 2799, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 7132, "tid": 47, "ts": 1750349492477300, "dur": 15636, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}