using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Status;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Base class for all combat actions (attacks, skills, items, etc.)
    /// </summary>
    public abstract class CombatAction : ScriptableObject, ICombatAction
    {
        [Header("Basic Info")]
        public string actionName = "New Action";
        [TextArea] public string description = "Action description";
        public Sprite icon;
        
        [Header("Targeting")]
        public TargetType targetType;
        public TargetTeam validTargets;
        public int range = 1;
        public bool requiresLineOfSight = true;
        
        [Header("Costs")]
        public int mpCost = 0;
        public int hpCost = 0;
        public int cooldown = 0;
        
        [Header("Effects")]
        public List<StatusEffect> statusEffects = new List<StatusEffect>();

        // Cooldown tracking (not serialized)
        [System.NonSerialized] private int currentCooldown = 0;

        // ICombatAction interface properties
        public string ActionName => actionName;
        public string Description => description;
        public Sprite Icon => icon;
        public bool CanTargetSelf => validTargets == TargetTeam.Self || validTargets == TargetTeam.Allies || validTargets == TargetTeam.All;
        public bool CanTargetAllies => validTargets == TargetTeam.Ally || validTargets == TargetTeam.Allies || validTargets == TargetTeam.All;
        public bool CanTargetEnemies => validTargets == TargetTeam.Enemy || validTargets == TargetTeam.All;
        public int ManaCost => mpCost;
        public int Cooldown => cooldown;
        public int CurrentCooldown
        {
            get => currentCooldown;
            set => currentCooldown = value;
        }
        
        /// <summary>
        /// Check if the action can be performed by the user
        /// </summary>
        public virtual bool CanBeUsedBy(ICombatParticipant user)
        {
            if (user == null || !user.IsAlive) return false;
            if (user.CurrentMP < mpCost) return false;
            if (user.CurrentHP <= hpCost) return false;
            if (currentCooldown > 0) return false;
            
            return true;
        }
        
        /// <summary>
        /// Check if the target is valid for this action
        /// </summary>
        public virtual bool IsValidTarget(ICombatParticipant user, ICombatParticipant target)
        {
            if (target == null) return false;
            
            // Check if target is on the right team
            switch (validTargets)
            {
                case TargetTeam.Self:
                    return target == user;
                case TargetTeam.Ally:
                    return target.IsPlayerControlled == user.IsPlayerControlled && target != user;
                case TargetTeam.Allies:
                    return target.IsPlayerControlled == user.IsPlayerControlled;
                case TargetTeam.Enemy:
                    return target.IsPlayerControlled != user.IsPlayerControlled;
                case TargetTeam.All:
                    return true;
                default:
                    return false;
            }
        }
        
        /// <summary>
        /// Get all valid targets for this action
        /// </summary>
        public virtual List<ICombatParticipant> GetValidTargets(ICombatParticipant user, List<ICombatParticipant> allParticipants)
        {
            List<ICombatParticipant> validTargets = new List<ICombatParticipant>();
            
            foreach (var participant in allParticipants)
            {
                if (IsValidTarget(user, participant))
                {
                    validTargets.Add(participant);
                }
            }
            
            return validTargets;
        }
        
        /// <summary>
        /// Execute the action (ICombatAction interface implementation)
        /// </summary>
        public virtual void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            // Default implementation - derived classes can override
            ExecuteCoroutine(user, target, null);
        }

        /// <summary>
        /// Execute the action with coroutine support
        /// </summary>
        public abstract IEnumerator ExecuteCoroutine(ICombatParticipant user, ICombatParticipant target, System.Action onComplete);
        
        /// <summary>
        /// Called when the action is selected in the UI
        /// </summary>
        public virtual void OnSelected(ICombatParticipant user) { }
        
        /// <summary>
        /// Called when the action is deselected in the UI
        /// </summary>
        public virtual void OnDeselected(ICombatParticipant user) { }
        
        /// <summary>
        /// Apply cooldown to this action
        /// </summary>
        public void ApplyCooldown()
        {
            if (cooldown > 0)
            {
                currentCooldown = cooldown;
            }
        }
        
        /// <summary>
        /// Reduce cooldown by the specified amount (usually 1 per turn)
        /// </summary>
        public void ReduceCooldown(int amount = 1)
        {
            currentCooldown = Mathf.Max(0, currentCooldown - amount);
        }
        
        /// <summary>
        /// Get the current cooldown
        /// </summary>
        public int GetCurrentCooldown() => currentCooldown;
        
        /// <summary>
        /// Reset cooldown to 0
        /// </summary>
        public void ResetCooldown()
        {
            currentCooldown = 0;
        }

        /// <summary>
        /// Play animation for the action (ICombatAction interface implementation)
        /// </summary>
        public virtual void PlayAnimation(ICombatParticipant user)
        {
            // Default implementation - derived classes can override
            // Could trigger animator parameters or play specific animations
        }

        /// <summary>
        /// Play visual effects for the action (ICombatAction interface implementation)
        /// </summary>
        public virtual void PlayVFX(Vector3 position)
        {
            // Default implementation - derived classes can override
            // Could instantiate particle effects or other visual feedback
        }
    }
    
    public enum TargetType
    {
        Self,
        SingleAlly,
        SingleEnemy,
        AllAllies,
        AllEnemies,
        AllInRange,
        AreaOfEffect
    }
    
    public enum TargetTeam
    {
        Self,
        Ally,
        Allies,
        Enemy,
        All
    }
}
